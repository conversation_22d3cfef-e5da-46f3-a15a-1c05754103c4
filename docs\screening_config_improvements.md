# 筛查配置界面改进说明

## 概述

根据用户反馈，我们对CMOST筛查配置界面进行了重要改进，添加了之前缺失的关键参数设置功能。

## 新增功能

### 1. 主要筛查工具的诊断性肠镜依从性

**问题**: 主要筛查工具（除肠镜外）没有阳性后接受诊断性肠镜筛查的依从性录入位置

**解决方案**: 
- 当选择非肠镜筛查工具（如FIT、乙状结肠镜等）时，自动显示"阳性后肠镜依从性"参数
- 默认值设置为80%
- 当选择肠镜作为主要筛查工具时，此参数自动隐藏（因为不需要后续诊断性肠镜）

### 2. 次要筛查工具的完整参数配置

**问题**: 启用贯序筛查中没有次要筛查工具的详细参数设置

**解决方案**: 添加了完整的次要筛查工具参数配置面板，包括：

#### 2.1 筛查参数
- **次要工具起始年龄**: 可选择40、45、50、55岁，默认50岁
- **次要工具结束年龄**: 可选择70、75、80、85岁，默认75岁

#### 2.2 筛查工具性能参数
- **次要工具腺瘤灵敏度**: 默认40%
- **次要工具癌症灵敏度**: 默认70%
- **次要工具特异度**: 默认95%
- **次要工具依从性**: 默认85%

#### 2.3 诊断性肠镜依从性
- **次要工具阳性后肠镜依从性**: 默认80%

## 界面交互逻辑

### 主要筛查工具变化响应
```python
def on_primary_tool_change(self, *args):
    """当主要筛查工具改变时的响应"""
    primary_tool = self.primary_tool_var.get()
    
    # 只有非肠镜工具才显示诊断性肠镜依从性
    if primary_tool != "colonoscopy":
        # 显示阳性后肠镜依从性参数
    else:
        # 隐藏阳性后肠镜依从性参数
```

### 贯序筛查开关响应
```python
def toggle_sequential_screening(self):
    """切换贯序筛查选项"""
    if self.sequential_screening_var.get():
        # 显示次要筛查工具选择
        # 显示次要筛查工具参数面板
    else:
        # 隐藏次要筛查工具选择
        # 隐藏次要筛查工具参数面板
```

## 参数验证

新增的参数验证包括：

1. **主要工具诊断依从性验证**
   - 仅在选择非肠镜工具时验证
   - 确保输入为有效数值

2. **次要工具参数验证**
   - 仅在启用贯序筛查时验证
   - 验证年龄范围的合理性（起始年龄 < 结束年龄）
   - 验证所有性能参数为有效数值

## 配置摘要显示

配置摘要现在包含：

### 主要筛查工具信息
- 工具类型
- 年龄范围
- 性能参数（腺瘤灵敏度、癌症灵敏度、特异度、依从性）
- 阳性后肠镜依从性（仅非肠镜工具）

### 次要筛查工具信息（贯序筛查时）
- 工具类型
- 年龄范围
- 性能参数（腺瘤灵敏度、癌症灵敏度、特异度、依从性）
- 阳性后肠镜依从性

## 默认值设置

| 参数 | 主要工具默认值 | 次要工具默认值 |
|------|---------------|---------------|
| 腺瘤灵敏度 | 85% | 40% |
| 癌症灵敏度 | 95% | 70% |
| 特异度 | 90% | 95% |
| 依从性 | 70% | 85% |
| 阳性后肠镜依从性 | 80% | 80% |

## 使用说明

1. **选择主要筛查工具**: 从下拉菜单选择
2. **设置主要工具参数**: 配置年龄范围和性能参数
3. **启用贯序筛查**（可选）: 勾选"启用贯序筛查"
4. **选择次要筛查工具**: 从下拉菜单选择
5. **设置次要工具参数**: 配置完整的参数集合
6. **验证配置**: 系统自动验证所有参数的有效性
7. **查看摘要**: 在配置摘要中确认所有设置

## 技术实现

- 使用动态UI显示/隐藏机制
- 参数变化时的实时响应
- 完整的输入验证
- 结构化的配置数据存储

这些改进确保了筛查配置的完整性和准确性，满足了复杂筛查策略的配置需求。
