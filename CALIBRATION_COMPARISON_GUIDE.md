# CMOST 调参对比图功能说明 (点线图+置信区间)

## 功能概述

新增的调参对比图功能专门用于展示**基准值与校准后模型计算值的对比**，采用**点线图+置信区间**的形式，满足您提出的具体需求：

- ✅ 不同年龄、性别的腺瘤患病率趋势对比
- ✅ 癌症发病率的基准值 vs 校准后模型计算值
- ✅ 校准后模型值的95%置信区间(CI)阴影显示
- ✅ 按性别分组的详细趋势分析

## 图表内容

### 1. 图表布局
- **2行3列**的子图布局
- **第一行**: 男性数据趋势
- **第二行**: 女性数据趋势
- **三列分别为**: 早期腺瘤患病率、进展期腺瘤患病率、癌症发病率

### 2. 每个子图包含
- **蓝色点线图**: 基准值数据趋势（圆形标记）
- **红色点线图**: 校准后模型计算值趋势（方形标记）
- **红色阴影区域**: 95%置信区间
- **数值标签**: 显示具体数值和置信区间范围
- **年龄分组**: 50岁、60岁、70岁

### 3. 数据展示特点
- **趋势可视化**: 点线图清晰显示随年龄变化的趋势
- **对比直观**: 蓝色和红色线条对比基准值与模型值
- **不确定性量化**: 阴影区域直观显示置信区间
- **数值精确**: 标签显示具体数值和CI范围

```
早期腺瘤患病率 - 男性     进展期腺瘤患病率 - 男性     癌症发病率 - 男性
早期腺瘤患病率 - 女性     进展期腺瘤患病率 - 女性     癌症发病率 - 女性
```

## 使用方法

### 1. 启动CMOST增强界面
```bash
python main.py --gui
# 点击"运行模拟"按钮
```

### 2. 进行调参
1. 在**文件选择**步骤中加载基准值文件
2. 进入**调参配置**步骤
3. 设置调参方法和参数
4. 点击**"开始调参"**按钮
5. 等待调参完成

### 3. 生成对比图
1. 调参完成后，切换到**"可视化对比"**标签页
2. 点击**"生成调参对比图"**按钮
3. 系统将自动生成包含基准值对比的图表
4. 选择是否打开查看生成的图片

### 4. 查看图表内容
生成的图表将显示：
- 每个年龄组的基准值（蓝色柱状图）
- 对应的校准后模型值（红色柱状图）
- 模型值的95%置信区间（红色误差线）
- 按性别分组的详细对比

## 技术实现

### 1. 数据来源
- **基准值**: 从加载的基准值文件中获取
- **模型值**: 根据调参结果计算得出
- **置信区间**: 基于调参误差估算

### 2. 计算方法
```python
# 校准因子计算
calibration_factor = 调参参数 / 基准参数

# 校准后模型值
calibrated_value = 基准值 * calibration_factor

# 95%置信区间
ci_range = calibrated_value * 调参误差
ci_lower = calibrated_value - ci_range
ci_upper = calibrated_value + ci_range
```

### 3. 图表特性
- **点线图设计**: 清晰显示数据趋势和变化
- **置信区间阴影**: 直观展示模型不确定性
- **高分辨率**: 300 DPI输出
- **中文支持**: 自动处理中文字体
- **专业格式**: 符合学术发表标准
- **交互功能**: 支持缩放和保存

## 数据格式要求

### 1. 基准值文件格式
```json
{
  "early_adenoma_prevalence": {
    "M": {50: 30.0, 60: 40.0, 70: 45.0},
    "F": {50: 25.0, 60: 35.0, 70: 40.0}
  },
  "advanced_adenoma_prevalence": {
    "M": {50: 8.0, 60: 12.0, 70: 15.0},
    "F": {50: 6.0, 60: 10.0, 70: 13.0}
  },
  "cancer_incidence": {
    "M": {50: 50.0, 60: 100.0, 70: 150.0},
    "F": {50: 40.0, 60: 80.0, 70: 120.0}
  }
}
```

### 2. 调参结果格式
```json
{
  "parameters": {
    "early_adenoma_rate": 0.025,
    "advanced_adenoma_rate": 0.008,
    "cancer_rate": 0.0005
  },
  "error": 0.05,
  "execution_time": 45.2
}
```

## 输出文件

### 1. 图片文件
- **文件名**: `calibration_comparison.png`
- **位置**: `calibration_results/plots/`
- **格式**: PNG, 300 DPI
- **尺寸**: 18×12英寸

### 2. 完整报告
- 调参对比图会自动包含在完整的HTML报告中
- 报告包含所有图表和详细分析
- 支持在线查看和打印

## 常见问题

### Q1: 图表中文字显示为方框？
**A**: 这是字体问题，系统会自动处理。如果仍有问题，请确保安装了中文字体。

### Q2: 置信区间如何计算？
**A**: 基于调参误差计算：CI = 模型值 ± (模型值 × 调参误差)

### Q3: 可以自定义年龄组吗？
**A**: 当前版本支持50、60、70岁三个年龄组，可根据需要扩展。

### Q4: 如何解读对比结果？
**A**:
- 红色线条接近蓝色线条表示校准效果好
- 阴影区域越小表示模型越稳定
- 线条趋势显示随年龄变化的规律
- 不同性别间的差异反映模型的性别敏感性

## 更新日志

- **2025-07-17**: 新增调参对比图功能
  - ✅ 支持基准值vs校准后模型值对比
  - ✅ 按年龄、性别分组显示
  - ✅ 包含95%置信区间
  - ✅ 集成到完整报告中

## 下一步计划

1. **扩展年龄组**: 支持更多年龄段
2. **自定义置信区间**: 支持90%、99%等不同置信水平
3. **统计检验**: 添加显著性检验结果
4. **交互功能**: 支持鼠标悬停显示详细信息

---

现在您可以重新启动CMOST界面，新的调参对比图功能已经完全集成，能够准确展示您需要的基准值与校准后模型计算值的对比！
