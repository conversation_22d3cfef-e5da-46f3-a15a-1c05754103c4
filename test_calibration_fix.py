#!/usr/bin/env python3
"""
测试调参对比图修复效果
验证是否显示完整的年龄范围（20-100岁）
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

def test_benchmark_data_loading():
    """测试基准数据加载"""
    print("=" * 60)
    print("测试基准数据加载")
    print("=" * 60)
    
    # 模拟 enhanced_main_window 的 _load_benchmark_data 方法
    def _load_benchmark_data():
        """Load benchmark data from file."""
        # This is a placeholder - actual implementation would load from file
        # 返回完整的年龄范围数据（20-100岁，每10岁一个数据点）
        return {
            'early_adenoma_prevalence': {
                'M': {
                    20: 5.0, 30: 10.0, 40: 18.0, 50: 30.0, 60: 40.0, 
                    70: 45.0, 80: 48.0, 90: 50.0, 100: 52.0
                },
                'F': {
                    20: 3.0, 30: 8.0, 40: 15.0, 50: 25.0, 60: 35.0, 
                    70: 40.0, 80: 42.0, 90: 44.0, 100: 45.0
                }
            },
            'advanced_adenoma_prevalence': {
                'M': {
                    20: 1.0, 30: 2.5, 40: 5.0, 50: 8.0, 60: 12.0, 
                    70: 15.0, 80: 17.0, 90: 18.0, 100: 19.0
                },
                'F': {
                    20: 0.8, 30: 2.0, 40: 4.0, 50: 6.0, 60: 10.0, 
                    70: 13.0, 80: 14.5, 90: 15.5, 100: 16.0
                }
            },
            'cancer_incidence': {
                'M': {
                    20: 2.0, 30: 8.0, 40: 25.0, 50: 50.0, 60: 100.0, 
                    70: 150.0, 80: 180.0, 90: 200.0, 100: 210.0
                },
                'F': {
                    20: 1.5, 30: 6.0, 40: 20.0, 50: 40.0, 60: 80.0, 
                    70: 120.0, 80: 140.0, 90: 155.0, 100: 165.0
                }
            }
        }
    
    benchmarks = _load_benchmark_data()
    
    # 检查数据点数量
    for disease, disease_data in benchmarks.items():
        print(f"\n{disease}:")
        for gender, gender_data in disease_data.items():
            ages = sorted(gender_data.keys())
            print(f"  {gender}: {len(ages)} 个年龄点 - {ages}")
    
    return benchmarks

def test_calibration_visualization():
    """测试调参可视化功能"""
    print("\n" + "=" * 60)
    print("测试调参可视化功能")
    print("=" * 60)
    
    try:
        from cmost.calibration.visualization import calibration_visualizer
        
        # 模拟调参结果
        mock_results = {
            'method': 'test',
            'parameters': {
                'early_adenoma_rate': 0.025,
                'advanced_adenoma_rate': 0.012,
                'cancer_rate': 0.0015
            },
            'error': 0.05,
            'history': []
        }
        
        # 获取基准数据
        benchmarks = test_benchmark_data_loading()
        
        # 生成调参对比图
        print("\n生成调参对比图...")
        plot_path = calibration_visualizer.plot_calibration_results_comparison(
            mock_results, benchmarks
        )
        
        if plot_path and os.path.exists(plot_path):
            print(f"✅ 调参对比图生成成功: {plot_path}")
            print("图表应该包含:")
            print("- 3种疾病类型（早期腺瘤、进展期腺瘤、癌症发病率）")
            print("- 2种性别（男性、女性）")
            print("- 9个年龄点（20-100岁，每10岁一个点）")
            print("- 点线图 + 置信区间阴影")
            return True
        else:
            print("❌ 调参对比图生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 测试调参对比图修复效果")
    print("验证是否显示完整的年龄范围（20-100岁）")
    
    # 测试基准数据加载
    benchmarks = test_benchmark_data_loading()
    
    # 测试可视化功能
    success = test_calibration_visualization()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 测试通过！调参对比图现在应该显示完整的年龄范围")
    else:
        print("❌ 测试失败！需要进一步检查")
    print("=" * 60)

if __name__ == "__main__":
    main()
