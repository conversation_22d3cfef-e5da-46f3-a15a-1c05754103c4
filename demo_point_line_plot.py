#!/usr/bin/env python3
"""
演示新的点线图+置信区间调参对比图功能

该脚本直接生成一个示例图表，展示新的可视化效果。
"""

import matplotlib.pyplot as plt
import numpy as np
import os

def create_demo_plot():
    """创建演示用的点线图+置信区间图表"""
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('调参结果对比：基准值 vs 校准后模型计算值 (点线图+置信区间)', 
                fontsize=16, fontweight='bold', y=0.95)
    
    # 定义年龄组和性别
    age_groups = [50, 60, 70]
    genders = ['M', 'F']
    gender_labels = {'M': '男性', 'F': '女性'}
    
    # 定义疾病类型
    diseases = ['early_adenoma', 'advanced_adenoma', 'cancer']
    disease_labels = {
        'early_adenoma': '早期腺瘤患病率',
        'advanced_adenoma': '进展期腺瘤患病率', 
        'cancer': '癌症发病率'
    }
    
    # 模拟数据
    demo_data = {
        'early_adenoma': {
            'M': {'benchmark': [30.0, 40.0, 45.0], 'model': [32.1, 38.5, 43.2]},
            'F': {'benchmark': [25.0, 35.0, 40.0], 'model': [26.8, 33.2, 38.9]}
        },
        'advanced_adenoma': {
            'M': {'benchmark': [8.0, 12.0, 15.0], 'model': [8.5, 11.2, 14.1]},
            'F': {'benchmark': [6.0, 10.0, 13.0], 'model': [6.3, 9.8, 12.5]}
        },
        'cancer': {
            'M': {'benchmark': [50.0, 100.0, 150.0], 'model': [52.3, 95.8, 145.2]},
            'F': {'benchmark': [40.0, 80.0, 120.0], 'model': [41.5, 78.2, 116.8]}
        }
    }
    
    # 为每种疾病和性别创建对比图
    for i, disease in enumerate(diseases):
        for j, gender in enumerate(genders):
            ax = axes[j, i]
            
            # 获取数据
            benchmark_values = demo_data[disease][gender]['benchmark']
            model_values = demo_data[disease][gender]['model']
            
            # 计算置信区间（模拟5%误差）
            error_rate = 0.05
            model_ci_lower = [v * (1 - error_rate) for v in model_values]
            model_ci_upper = [v * (1 + error_rate) for v in model_values]
            
            # 绘制基准值点线图
            ax.plot(age_groups, benchmark_values, 'o-', 
                   label='基准值', color='blue', linewidth=2, 
                   markersize=8, markerfacecolor='lightblue', 
                   markeredgecolor='blue', markeredgewidth=2)
            
            # 绘制模型计算值点线图
            ax.plot(age_groups, model_values, 's-', 
                   label='校准后模型值', color='red', linewidth=2, 
                   markersize=8, markerfacecolor='lightcoral', 
                   markeredgecolor='red', markeredgewidth=2)
            
            # 添加95% CI置信区间阴影
            ax.fill_between(age_groups, model_ci_lower, model_ci_upper, 
                           color='red', alpha=0.2, label='95% 置信区间')
            
            # 设置图表属性
            ax.set_title(f'{disease_labels[disease]} - {gender_labels[gender]}', 
                       fontsize=12, fontweight='bold')
            ax.set_xlabel('年龄', fontsize=10)
            ax.set_ylabel('率值 (每10万人)', fontsize=10)
            ax.set_xticks(age_groups)
            ax.legend(fontsize=9, loc='best')
            ax.grid(True, alpha=0.3, linestyle='--')
            
            # 设置坐标轴范围
            all_values = benchmark_values + model_values + model_ci_lower + model_ci_upper
            y_min = min(all_values) * 0.9
            y_max = max(all_values) * 1.1
            ax.set_ylim(y_min, y_max)
            
            # 添加数值标签
            for x, y in zip(age_groups, benchmark_values):
                ax.annotate(f'{y:.1f}', (x, y), textcoords="offset points", 
                          xytext=(0,10), ha='center', fontsize=8, color='blue')
            
            for x, y in zip(age_groups, model_values):
                ax.annotate(f'{y:.1f}', (x, y), textcoords="offset points", 
                          xytext=(0,-15), ha='center', fontsize=8, color='red')
            
            # 添加置信区间数值标签
            for x, lower, upper in zip(age_groups, model_ci_lower, model_ci_upper):
                ax.annotate(f'[{lower:.1f}, {upper:.1f}]', (x, upper), 
                          textcoords="offset points", xytext=(0,5), 
                          ha='center', fontsize=7, color='gray', alpha=0.8)
    
    plt.tight_layout()
    
    # 保存图片
    output_path = "demo_calibration_comparison.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    return output_path

def main():
    """主函数"""
    print("生成演示用的调参对比图（点线图+置信区间）...")
    
    try:
        output_path = create_demo_plot()
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"✓ 演示图表生成成功: {output_path}")
            print(f"✓ 文件大小: {file_size} bytes")
            
            print("\n图表特点:")
            print("- 点线图显示不同年龄的趋势变化")
            print("- 蓝色圆形标记: 基准值")
            print("- 红色方形标记: 校准后模型值")
            print("- 红色阴影区域: 95%置信区间")
            print("- 数值标签显示具体数值和CI范围")
            print("- 按性别分组的2行3列布局")
            
            # 询问是否打开图片
            try:
                import platform
                import subprocess
                
                response = input("\n是否打开查看生成的图片? (y/n): ").lower().strip()
                if response in ['y', 'yes', '是']:
                    if platform.system() == "Windows":
                        os.startfile(output_path)
                    elif platform.system() == "Darwin":  # macOS
                        subprocess.run(["open", output_path])
                    else:  # Linux
                        subprocess.run(["xdg-open", output_path])
                    print("图片已在默认程序中打开")
                else:
                    print("图片已保存，您可以手动打开查看")
                    
            except Exception as e:
                print(f"无法自动打开图片: {e}")
                print("请手动打开查看生成的图片")
            
            return True
        else:
            print("✗ 演示图表生成失败")
            return False
            
    except Exception as e:
        print(f"✗ 生成演示图表时发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 演示完成！新的点线图+置信区间功能已准备就绪。")
    else:
        print("\n❌ 演示失败，请检查相关依赖。")
