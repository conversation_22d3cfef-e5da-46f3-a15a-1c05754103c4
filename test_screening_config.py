#!/usr/bin/env python3
"""
测试筛查配置界面的新功能
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from cmost.ui.enhanced_main_window import EnhancedMainWindow

def test_screening_config():
    """测试筛查配置界面"""
    print("启动筛查配置测试...")
    
    # 创建主窗口
    root = tk.Tk()
    root.title("CMOST 筛查配置测试")
    root.geometry("1200x800")
    
    # 创建增强主窗口
    app = EnhancedMainWindow(root)
    
    # 直接跳转到筛查配置步骤
    app.show_step(2)
    
    print("筛查配置界面已启动")
    print("新功能包括:")
    print("1. 主要筛查工具（除肠镜外）的阳性后接受诊断性肠镜筛查的依从性")
    print("2. 次要筛查工具的详细参数设置")
    print("3. 次要筛查工具的年龄范围设置")
    print("4. 次要筛查工具的性能参数设置")
    print("5. 次要筛查工具的阳性后肠镜依从性设置")
    
    # 启动GUI
    root.mainloop()

if __name__ == "__main__":
    test_screening_config()
