"""
Auto-calibration module for CMOST using machine learning approaches.

This module implements a one-step calibration approach using deep neural networks
to replace the traditional three-step calibration process (early adenoma, advanced adenoma,
and cancer incidence) that used greedy algorithms and Nelder-Mead optimization.

Reference:
    VAHDAT V, et al. Medical Decision Making, 2023, 43(6): 719-736
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from pyDOE3 import lhs
from typing import Dict, List, Tuple, Optional, Union, Callable
import matplotlib.pyplot as plt
from tqdm import tqdm
import os
import pickle
from datetime import datetime

from ..core.simulation import Simulation
from ..core.progression import ProgressionModel
from .benchmark import load_benchmarks


class CalibrationNN(nn.Module):
    """Neural network for calibration parameter prediction."""
    
    def __init__(self, input_dim: int, hidden_dims: List[int], output_dim: int):
        """Initialize the neural network.
        
        Args:
            input_dim: Dimension of input (benchmark data)
            hidden_dims: List of hidden layer dimensions
            output_dim: Dimension of output (calibration parameters)
        """
        super(CalibrationNN, self).__init__()
        
        layers = []
        prev_dim = input_dim
        
        # Create hidden layers
        for dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, dim))
            layers.append(nn.ReLU())
            layers.append(nn.BatchNorm1d(dim))
            prev_dim = dim
        
        # Output layer
        layers.append(nn.Linear(prev_dim, output_dim))
        
        self.model = nn.Sequential(*layers)
    
    def forward(self, x):
        """Forward pass through the network.
        
        Args:
            x: Input tensor
            
        Returns:
            torch.Tensor: Output tensor
        """
        return self.model(x)


class AutoCalibration:
    """Auto-calibration using machine learning for CMOST."""
    
    def __init__(
        self,
        benchmarks: Dict = None,
        simulation: Optional[Simulation] = None,
        device: str = "cuda" if torch.cuda.is_available() else "cpu",
        output_dir: str = "calibration_results"
    ):
        """Initialize the auto-calibration module.
        
        Args:
            benchmarks: Dictionary of benchmark data or None to load defaults
            simulation: Optional simulation instance
            device: Device to run neural network on ('cuda' or 'cpu')
            output_dir: Directory to save results
        """
        self.benchmarks = benchmarks if benchmarks else load_benchmarks()
        self.simulation = simulation
        self.device = device
        self.output_dir = output_dir
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Parameter ranges for Latin Hypercube Sampling
        self.param_ranges = {
            # Early adenoma generation (sigmoid function parameters)
            'early_mult': (1.0, 10.0),      # Multiplicative factor
            'early_width': (0.1, 1.0),      # Width factor
            'early_center': (20.0, 80.0),   # Center (age)
            
            # Early to advanced adenoma progression (sigmoid function parameters)
            'adv_mult': (0.5, 5.0),         # Multiplicative factor
            'adv_width': (0.1, 1.0),        # Width factor
            'adv_center': (20.0, 80.0),     # Center (age)
            
            # Advanced adenoma to cancer progression (sigmoid function parameters)
            'cancer_mult': (0.1, 2.0),      # Multiplicative factor
            'cancer_width': (0.1, 1.0),     # Width factor
            'cancer_center': (20.0, 80.0)   # Center (age)
        }
        
        # Initialize neural network
        self.model = None
        self.scaler_x = None
        self.scaler_y = None
        
        # Tracking
        self.training_history = {
            'train_loss': [],
            'val_loss': []
        }
        
        # EMA model for more stable predictions
        self.ema_model = None
    
    def generate_lhs_samples(self, n_samples: int = 10000) -> pd.DataFrame:
        """Generate Latin Hypercube Sampling of parameter space.
        
        Args:
            n_samples: Number of samples to generate
            
        Returns:
            pd.DataFrame: DataFrame of parameter samples
        """
        # Generate normalized LHS samples (0-1)
        lhs_samples = lhs(len(self.param_ranges), samples=n_samples)
        
        # Scale to parameter ranges
        param_names = list(self.param_ranges.keys())
        scaled_samples = np.zeros_like(lhs_samples)
        
        for i, param in enumerate(param_names):
            low, high = self.param_ranges[param]
            scaled_samples[:, i] = lhs_samples[:, i] * (high - low) + low
        
        # Convert to DataFrame
        return pd.DataFrame(scaled_samples, columns=param_names)
    
    def run_simulations(
        self, 
        param_samples: pd.DataFrame, 
        n_patients: int = 10000,
        progress_bar: bool = True
    ) -> pd.DataFrame:
        """Run simulations with sampled parameters and collect outputs.
        
        Args:
            param_samples: DataFrame of parameter samples
            n_patients: Number of patients per simulation
            progress_bar: Whether to show progress bar
            
        Returns:
            pd.DataFrame: DataFrame of simulation outputs
        """
        results = []
        iterator = tqdm(param_samples.iterrows(), total=len(param_samples)) if progress_bar else param_samples.iterrows()
        
        for i, params in iterator:
            # Create progression model with current parameters
            progression_model = ProgressionModel()
            
            # Set early adenoma parameters
            progression_model.set_early_adenoma_params(
                mult=params['early_mult'],
                width=params['early_width'],
                center=params['early_center']
            )
            
            # Set advanced adenoma parameters
            progression_model.set_advanced_adenoma_params(
                mult=params['adv_mult'],
                width=params['adv_width'],
                center=params['adv_center']
            )
            
            # Set cancer progression parameters
            progression_model.set_cancer_params(
                mult=params['cancer_mult'],
                width=params['cancer_width'],
                center=params['cancer_center']
            )
            
            # Create simulation with this progression model
            sim = Simulation(progression_model=progression_model)
            
            # Run simulation
            sim.generate_population(n_patients)
            sim.run(years=50)  # Run for 50 years to reach steady state
            
            # Collect outputs
            outputs = {
                # Input parameters
                'early_mult': params['early_mult'],
                'early_width': params['early_width'],
                'early_center': params['early_center'],
                'adv_mult': params['adv_mult'],
                'adv_width': params['adv_width'],
                'adv_center': params['adv_center'],
                'cancer_mult': params['cancer_mult'],
                'cancer_width': params['cancer_width'],
                'cancer_center': params['cancer_center'],
                
                # Output metrics
                **self._extract_simulation_metrics(sim)
            }
            
            results.append(outputs)
        
        return pd.DataFrame(results)
    
    def _extract_simulation_metrics(self, simulation: Simulation) -> Dict[str, float]:
        """Extract relevant metrics from simulation for calibration.
        
        Args:
            simulation: Simulation instance
            
        Returns:
            Dict[str, float]: Dictionary of metrics
        """
        metrics = {}
        
        # Extract early adenoma prevalence by age and gender
        for gender in ['M', 'F']:
            for age in range(20, 81, 10):
                key = f'early_prev_{gender}_{age}'
                metrics[key] = simulation.get_early_adenoma_prevalence(age=age, gender=gender)
        
        # Extract advanced adenoma prevalence by age and gender
        for gender in ['M', 'F']:
            for age in range(20, 81, 10):
                key = f'adv_prev_{gender}_{age}'
                metrics[key] = simulation.get_advanced_adenoma_prevalence(age=age, gender=gender)
        
        # Extract preclinical cancer dwell time
        metrics['preclinical_dwell_time'] = simulation.get_average_preclinical_dwell_time()
        
        # Extract cancer incidence by age and gender
        for gender in ['M', 'F']:
            for age in range(30, 81, 10):
                key = f'cancer_inc_{gender}_{age}'
                metrics[key] = simulation.get_cancer_incidence(age=age, gender=gender)
        
        # Extract cancer mortality by age and gender
        for gender in ['M', 'F']:
            for age in range(30, 81, 10):
                key = f'cancer_mort_{gender}_{age}'
                metrics[key] = simulation.get_cancer_mortality(age=age, gender=gender)
        
        return metrics
    
    def prepare_training_data(
        self, 
        simulation_results: pd.DataFrame,
        test_size: float = 0.2,
        normalize: bool = True
    ) -> Tuple:
        """Prepare training data for neural network.
        
        Args:
            simulation_results: DataFrame of simulation results
            test_size: Fraction of data to use for testing
            normalize: Whether to normalize data
            
        Returns:
            Tuple: (train_loader, val_loader, input_dim, output_dim)
        """
        # Separate inputs (metrics) and outputs (parameters)
        param_cols = list(self.param_ranges.keys())
        metric_cols = [col for col in simulation_results.columns if col not in param_cols]
        
        X = simulation_results[metric_cols].values
        y = simulation_results[param_cols].values
        
        # Normalize data
        if normalize:
            from sklearn.preprocessing import StandardScaler
            self.scaler_x = StandardScaler()
            self.scaler_y = StandardScaler()
            
            X = self.scaler_x.fit_transform(X)
            y = self.scaler_y.fit_transform(y)
        
        # Split into train and validation sets
        from sklearn.model_selection import train_test_split
        X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=test_size, random_state=42)
        
        # Convert to PyTorch tensors
        X_train_tensor = torch.FloatTensor(X_train)
        y_train_tensor = torch.FloatTensor(y_train)
        X_val_tensor = torch.FloatTensor(X_val)
        y_val_tensor = torch.FloatTensor(y_val)
        
        # Create datasets and dataloaders
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
        
        train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=64, shuffle=False)
        
        return train_loader, val_loader, X_train.shape[1], y_train.shape[1]
    
    def train_model(
        self, 
        train_loader: DataLoader, 
        val_loader: DataLoader,
        input_dim: int,
        output_dim: int,
        hidden_dims: List[int] = [256, 128, 64],
        lr: float = 0.001,
        epochs: int = 200,
        patience: int = 10,
        use_ema: bool = True,
        ema_decay: float = 0.999
    ) -> nn.Module:
        """Train neural network for calibration.
        
        Args:
            train_loader: DataLoader for training data
            val_loader: DataLoader for validation data
            input_dim: Input dimension
            output_dim: Output dimension
            hidden_dims: List of hidden layer dimensions
            lr: Learning rate
            epochs: Number of epochs
            patience: Patience for early stopping
            use_ema: Whether to use EMA model
            ema_decay: EMA decay rate
            
        Returns:
            nn.Module: Trained neural network
        """
        # Initialize model
        self.model = CalibrationNN(input_dim, hidden_dims, output_dim).to(self.device)
        
        # Initialize EMA model if requested
        if use_ema:
            self.ema_model = torch.optim.swa_utils.AveragedModel(
                self.model, 
                multi_avg_fn=torch.optim.swa_utils.get_ema_multi_avg_fn(ema_decay)
            )
        
        # Loss function and optimizer
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self.model.parameters(), lr=lr)
        
        # Learning rate scheduler
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5)
        
        # Training loop
        best_val_loss = float('inf')
        no_improve_epochs = 0
        
        for epoch in range(epochs):
            # Training
            self.model.train()
            train_loss = 0.0
            
            for inputs, targets in train_loader:
                inputs, targets = inputs.to(self.device), targets.to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(inputs)
                loss = criterion(outputs, targets)
                loss.backward()
                optimizer.step()
                
                # Update EMA model
                if use_ema:
                    self.ema_model.update_parameters(self.model)
                
                train_loss += loss.item()
            
            train_loss /= len(train_loader)
            
            # Validation
            self.model.eval()
            val_loss = 0.0
            
            with torch.no_grad():
                for inputs, targets in val_loader:
                    inputs, targets = inputs.to(self.device), targets.to(self.device)
                    
                    if use_ema:
                        outputs = self.ema_model(inputs)
                    else:
                        outputs = self.model(inputs)
                        
                    loss = criterion(outputs, targets)
                    val_loss += loss.item()
            
            val_loss /= len(val_loader)
            
            # Update learning rate
            scheduler.step(val_loss)
            
            # Track history
            self.training_history['train_loss'].append(train_loss)
            self.training_history['val_loss'].append(val_loss)
            
            # Print progress
            print(f'Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')
            
            # Check for improvement
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                no_improve_epochs = 0
                
                # Save best model
                if use_ema:
                    torch.save(self.ema_model.state_dict(), os.path.join(self.output_dir, 'best_model_ema.pt'))
                torch.save(self.model.state_dict(), os.path.join(self.output_dir, 'best_model.pt'))
            else:
                no_improve_epochs += 1
            
            # Early stopping
            if no_improve_epochs >= patience:
                print(f'Early stopping after {epoch+1} epochs')
                break
        
        # Load best model
        if use_ema:
            self.ema_model.load_state_dict(torch.load(os.path.join(self.output_dir, 'best_model_ema.pt')))
            return self.ema_model
        else:
            self.model.load_state_dict(torch.load(os.path.join(self.output_dir, 'best_model.pt')))
            return self.model
    
    def plot_training_history(self):
        """Plot training and validation loss history."""
        plt.figure(figsize=(10, 6))
        plt.plot(self.training_history['train_loss'], label='Training Loss')
        plt.plot(self.training_history['val_loss'], label='Validation Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Training and Validation Loss')
        plt.legend()
        plt.grid(True)
        plt.savefig(os.path.join(self.output_dir, 'training_history.png'))
        plt.close()
    
    def predict_parameters(self, target_metrics: Dict[str, float]) -> Dict[str, float]:
        """Predict calibration parameters from target metrics.
        
        Args:
            target_metrics: Dictionary of target metrics
            
        Returns:
            Dict[str, float]: Dictionary of predicted parameters
        """
        if self.model is None:
            raise ValueError("Model not trained. Call train_model first.")
        
        # Convert target metrics to array in the correct order
        metric_cols = [col for col in self.simulation_results.columns 
                      if col not in list(self.param_ranges.keys())]
        
        X = np.array([target_metrics.get(col, 0.0) for col in metric_cols]).reshape(1, -1)
        
        # Normalize
        if self.scaler_x is not None:
            X = self.scaler_x.transform(X)
        
        # Convert to tensor
        X_tensor = torch.FloatTensor(X).to(self.device)
        
        # Predict
        self.model.eval()
        with torch.no_grad():
            if self.ema_model is not None:
                y_pred = self.ema_model(X_tensor).cpu().numpy()
            else:
                y_pred = self.model(X_tensor).cpu().numpy()
        
        # Denormalize
        if self.scaler_y is not None:
            y_pred = self.scaler_y.inverse_transform(y_pred)
        
        # Convert to dictionary
        param_names = list(self.param_ranges.keys())
        return {param: float(y_pred[0, i]) for i, param in enumerate(param_names)}
    
    def run_calibration_pipeline(
        self,
        n_samples: int = 10000,
        n_patients: int = 10000,
        hidden_dims: List[int] = [256, 128, 64],
        epochs: int = 200,
        use_ema: bool = True
    ) -> Dict[str, float]:
        """Run the full calibration pipeline.
        
        Args:
            n_samples: Number of LHS samples
            n_patients: Number of patients per simulation
            hidden_dims: Hidden layer dimensions for neural network
            epochs: Number of training epochs
            use_ema: Whether to use EMA model
            
        Returns:
            Dict[str, float]: Calibrated parameters
        """
        print(f"Starting calibration pipeline with {n_samples} samples")
        
        # Step 1: Generate parameter samples using LHS
        print("Generating parameter samples using Latin Hypercube Sampling...")
        param_samples = self.generate_lhs_samples(n_samples)
        
        # Step 2: Run simulations for each parameter set
        print(f"Running {n_samples} simulations...")
        self.simulation_results = self.run_simulations(param_samples, n_patients)
        
        # Save simulation results
        self.simulation_results.to_csv(os.path.join(self.output_dir, 'simulation_results.csv'), index=False)
        
        # Step 3: Prepare training data
        print("Preparing training data...")
        train_loader, val_loader, input_dim, output_dim = self.prepare_training_data(self.simulation_results)
        
        # Step 4: Train neural network
        print(f"Training neural network with {epochs} epochs...")
        self.train_model(
            train_loader, 
            val_loader, 
            input_dim, 
            output_dim, 
            hidden_dims=hidden_dims,
            epochs=epochs,
            use_ema=use_ema
        )
        
        # Plot training history
        self.plot_training_history()
        
        # Step 5: Predict parameters for target metrics (benchmarks)
        print("Predicting parameters for target benchmarks...")
        target_metrics = self._prepare_benchmark_metrics()
        calibrated_params = self.predict_parameters(target_metrics)
        
        # Save calibrated parameters
        with open(os.path.join(self.output_dir, 'calibrated_params.pkl'), 'wb') as f:
            pickle.dump(calibrated_params, f)
        
        # Print calibrated parameters
        print("Calibration complete. Calibrated parameters:")
        for param, value in calibrated_params.items():
            print(f"  {param}: {value:.6f}")
        
        return calibrated_params
    
    def _prepare_benchmark_metrics(self) -> Dict[str, float]:
        """Prepare benchmark metrics in the format expected by the model.
        
        Returns:
            Dict[str, float]: Dictionary of benchmark metrics
        """
        metrics = {}
        
        # Early adenoma prevalence by age and gender
        for gender in ['M', 'F']:
            for age in range(20, 81, 10):
                key = f'early_prev_{gender}_{age}'
                metrics[key] = self.benchmarks.get(f'early_adenoma_prevalence_{gender}_{age}', 0.0)
        
        # Advanced adenoma prevalence by age and gender
        for gender in ['M', 'F']:
            for age in range(20, 81, 10):
                key = f'adv_prev_{gender}_{age}'
                metrics[key] = self.benchmarks.get(f'advanced_adenoma_prevalence_{gender}_{age}', 0.0)
        
        # Preclinical cancer dwell time
        metrics['preclinical_dwell_time'] = self.benchmarks.get('preclinical_dwell_time', 3.0)
        
        # Cancer incidence by age and gender
        for gender in ['M', 'F']:
            for age in range(30, 81, 10):
                key = f'cancer_inc_{gender}_{age}'
                metrics[key] = self.benchmarks.get(f'cancer_incidence_{gender}_{age}', 0.0)
        
        # Cancer mortality by age and gender
        for gender in ['M', 'F']:
            for age in range(30, 81, 10):
                key = f'cancer_mort_{gender}_{age}'
                metrics[key] = self.benchmarks.get(f'cancer_mortality_{gender}_{age}', 0.0)
        
        # Rectal cancer fraction by age and gender
        for gender in ['M', 'F']:
            for age in range(50, 81, 10):
                key = f'rectal_frac_{gender}_{age}'
                metrics[key] = self.benchmarks.get(f'rectal_cancer_fraction_{gender}_{age}', 0.0)
        
        # Preclinical dwell time
        metrics['preclinical_dwell_time'] = self.benchmarks.get('preclinical_dwell_time', 3.0)
        
        return metrics
    
    def validate_calibration(self, calibrated_params: Dict[str, float], n_patients: int = 100000) -> Dict:
        """Validate calibration by running a simulation with calibrated parameters.
        
        Args:
            calibrated_params: Dictionary of calibrated parameters
            n_patients: Number of patients for validation
            
        Returns:
            Dict: Dictionary of validation metrics
        """
        print(f"Validating calibration with {n_patients} patients...")
        
        # Create progression model with calibrated parameters
        progression_model = ProgressionModel()
        
        # Set early adenoma parameters
        progression_model.set_early_adenoma_params(
            mult=calibrated_params['early_mult'],
            width=calibrated_params['early_width'],
            center=calibrated_params['early_center']
        )
        
        # Set advanced adenoma parameters
        progression_model.set_advanced_adenoma_params(
            mult=calibrated_params['adv_mult'],
            width=calibrated_params['adv_width'],
            center=calibrated_params['adv_center']
        )
        
        # Set cancer progression parameters
        progression_model.set_cancer_params(
            mult=calibrated_params['cancer_mult'],
            width=calibrated_params['cancer_width'],
            center=calibrated_params['cancer_center']
        )
        
        # Create simulation with this progression model
        sim = Simulation(progression_model=progression_model)
        
        # Run simulation
        sim.generate_population(n_patients)
        sim.run(years=50)  # Run for 50 years to reach steady state
        
        # Extract metrics
        validation_metrics = self._extract_simulation_metrics(sim)
        
        # Compare with benchmarks
        benchmark_metrics = self._prepare_benchmark_metrics()
        comparison = {}
        
        for key in validation_metrics:
            if key in benchmark_metrics:
                sim_value = validation_metrics[key]
                bench_value = benchmark_metrics[key]
                rel_error = abs(sim_value - bench_value) / (bench_value + 1e-10) * 100
                
                comparison[key] = {
                    'simulation': sim_value,
                    'benchmark': bench_value,
                    'rel_error_pct': rel_error
                }
        
        # Save validation results
        validation_df = pd.DataFrame([
            {'metric': k, 'simulation': v['simulation'], 'benchmark': v['benchmark'], 'rel_error_pct': v['rel_error_pct']}
            for k, v in comparison.items()
        ])
        validation_df.to_csv(os.path.join(self.output_dir, 'validation_results.csv'), index=False)
        
        # Plot validation results
        self._plot_validation_results(comparison)
        
        return comparison
    
    def _plot_validation_results(self, comparison: Dict):
        """Plot validation results.
        
        Args:
            comparison: Dictionary of comparison metrics
        """
        # Group metrics by type
        metric_groups = {
            'Early Adenoma Prevalence': [k for k in comparison.keys() if k.startswith('early_prev')],
            'Advanced Adenoma Prevalence': [k for k in comparison.keys() if k.startswith('adv_prev')],
            'Cancer Incidence': [k for k in comparison.keys() if k.startswith('cancer_inc')],
            'Cancer Mortality': [k for k in comparison.keys() if k.startswith('cancer_mort')]
        }
        
        # Plot each group
        for group_name, metrics in metric_groups.items():
            plt.figure(figsize=(12, 8))
            
            # Extract age from metric name
            ages = sorted(list(set([int(m.split('_')[-1]) for m in metrics])))
            
            # Plot male and female separately
            for gender, color in [('M', 'blue'), ('F', 'red')]:
                # Simulation values
                sim_values = [comparison[f"{metrics[0].rsplit('_', 1)[0]}_{gender}_{age}"]['simulation'] 
                             for age in ages if f"{metrics[0].rsplit('_', 1)[0]}_{gender}_{age}" in comparison]
                
                # Benchmark values
                bench_values = [comparison[f"{metrics[0].rsplit('_', 1)[0]}_{gender}_{age}"]['benchmark'] 
                               for age in ages if f"{metrics[0].rsplit('_', 1)[0]}_{gender}_{age}" in comparison]
                
                # Plot
                plt.plot(ages[:len(sim_values)], sim_values, f'{color}-', marker='o', label=f'{gender} Simulation')
                plt.plot(ages[:len(bench_values)], bench_values, f'{color}--', marker='s', label=f'{gender} Benchmark')
            
            plt.xlabel('Age')
            plt.ylabel('Rate')
            plt.title(f'{group_name} - Simulation vs Benchmark')
            plt.legend()
            plt.grid(True)
            plt.savefig(os.path.join(self.output_dir, f'{group_name.lower().replace(" ", "_")}_validation.png'))
            plt.close()
        
        # Plot preclinical dwell time if available
        if 'preclinical_dwell_time' in comparison:
            plt.figure(figsize=(8, 6))
            sim_value = comparison['preclinical_dwell_time']['simulation']
            bench_value = comparison['preclinical_dwell_time']['benchmark']
            
            plt.bar(['Simulation', 'Benchmark'], [sim_value, bench_value])
            plt.ylabel('Years')
            plt.title('Preclinical Dwell Time - Simulation vs Benchmark')
            plt.grid(True, axis='y')
            plt.savefig(os.path.join(self.output_dir, 'preclinical_dwell_time_validation.png'))
            plt.close()


def run_calibration(
    output_dir: str = "calibration_results",
    n_samples: int = 10000,
    n_patients: int = 10000,
    hidden_dims: List[int] = [256, 128, 64],
    epochs: int = 200
) -> Dict[str, float]:
    """Run the calibration pipeline.
    
    Args:
        output_dir: Directory to save results
        n_samples: Number of LHS samples
        n_patients: Number of patients per simulation
        hidden_dims: Hidden layer dimensions for neural network
        epochs: Number of training epochs
        
    Returns:
        Dict[str, float]: Calibrated parameters
    """
    # Create output directory with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"{output_dir}_{timestamp}"
    
    # Initialize calibration
    calibration = AutoCalibration(output_dir=output_dir)
    
    # Run calibration pipeline
    calibrated_params = calibration.run_calibration_pipeline(
        n_samples=n_samples,
        n_patients=n_patients,
        hidden_dims=hidden_dims,
        epochs=epochs
    )
    
    # Validate calibration
    calibration.validate_calibration(calibrated_params)
    
    return calibrated_params


if __name__ == "__main__":
    # Example usage
    calibrated_params = run_calibration(
        n_samples=1000,  # Reduced for testing
        n_patients=1000,  # Reduced for testing
        epochs=50        # Reduced for testing
    )
