#!/usr/bin/env python3
"""
测试模拟控制功能（暂停、停止）
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from cmost.ui.enhanced_main_window import EnhancedMainWindow

def test_simulation_controls():
    """测试模拟控制功能"""
    print("启动模拟控制测试...")
    
    # 创建主窗口
    root = tk.Tk()
    root.title("CMOST 模拟控制测试")
    root.geometry("1200x800")
    
    # 创建增强主窗口
    app = EnhancedMainWindow(root)
    
    # 设置一些基本配置以便能够运行模拟
    app.setting_file_path = "test_setting.xlsx"
    app.benchmark_file_path = "test_benchmark.csv"
    app.num_patients_var.set("10000")
    app.sim_years_var.set("50")
    app.random_seed_var.set("42")

    # 初始化筛查配置变量（避免AttributeError）
    app.primary_tool_var = tk.StringVar(value="colonoscopy")
    app.sequential_screening_var = tk.BooleanVar(value=False)
    app.start_age_var = tk.StringVar(value="50")
    app.end_age_var = tk.StringVar(value="75")
    app.adenoma_sensitivity_var = tk.StringVar(value="85")
    app.cancer_sensitivity_var = tk.StringVar(value="95")
    app.specificity_var = tk.StringVar(value="90")
    app.compliance_var = tk.StringVar(value="70")
    app.diagnostic_compliance_var = tk.StringVar(value="80")

    # 初始化输出配置变量
    app.output_formats = {
        'Excel': tk.BooleanVar(value=True),
        'CSV': tk.BooleanVar(value=False),
        'JSON': tk.BooleanVar(value=False)
    }
    app.output_dir_var = tk.StringVar(value="./results")
    app.filename_prefix_var = tk.StringVar(value="cmost_simulation")

    # 直接跳转到模拟执行步骤
    app.show_step(4)
    
    print("模拟控制界面已启动")
    print("测试功能:")
    print("1. 点击'开始模拟'按钮启动模拟")
    print("2. 在模拟运行时点击'暂停模拟'按钮测试暂停功能")
    print("3. 在暂停状态下点击'恢复模拟'按钮测试恢复功能")
    print("4. 点击'停止模拟'按钮测试停止功能")
    print("5. 观察按钮状态和进度显示的变化")
    
    # 启动GUI
    root.mainloop()

if __name__ == "__main__":
    test_simulation_controls()
